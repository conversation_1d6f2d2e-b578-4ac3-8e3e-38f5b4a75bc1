<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="23">
            <item index="0" class="java.lang.String" itemvalue="face-recognition-models" />
            <item index="1" class="java.lang.String" itemvalue="tensorflow" />
            <item index="2" class="java.lang.String" itemvalue="opencv-python" />
            <item index="3" class="java.lang.String" itemvalue="PyQt5" />
            <item index="4" class="java.lang.String" itemvalue="pyqt5-tools" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="Pillow" />
            <item index="7" class="java.lang.String" itemvalue="thop" />
            <item index="8" class="java.lang.String" itemvalue="scipy" />
            <item index="9" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="10" class="java.lang.String" itemvalue="torch" />
            <item index="11" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="12" class="java.lang.String" itemvalue="ultralytics" />
            <item index="13" class="java.lang.String" itemvalue="contourpy" />
            <item index="14" class="java.lang.String" itemvalue="psutil" />
            <item index="15" class="java.lang.String" itemvalue="pyyaml" />
            <item index="16" class="java.lang.String" itemvalue="fonttools" />
            <item index="17" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="18" class="java.lang.String" itemvalue="seaborn" />
            <item index="19" class="java.lang.String" itemvalue="zipp" />
            <item index="20" class="java.lang.String" itemvalue="matplotlib" />
            <item index="21" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="22" class="java.lang.String" itemvalue="urllib3" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>