# -*- coding: utf-8 -*-
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog, QMessageBox, QWidget, QHeaderView, QTableWidgetItem, QAbstractItemView, QTextEdit
from PyQt5 import QtCore, QtWidgets
import sys
import os
from PIL import ImageFont
from ultralytics import YOLO
sys.path.append('UIProgram')
from UIProgram.UiMain import Ui_MainWindow
import sys
from PyQt5.QtCore import QTimer, Qt, QThread, pyqtSignal,QCoreApplication
import detect_tools as tools
import cv2
import Config
from UIProgram.QssLoader import QSSLoader
from UIProgram.precess_bar import ProgressBar
import numpy as np
# import torch

class MainWindow(QMainWindow):
    def __init__(self, parent=None):
        super(QMainWindow, self).__init__(parent)
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)

        # 设置窗口标题和图标
        self.setWindowTitle("基于YOLOv8的爬行动物种类检测与识别系统")

        # 设置窗口大小和位置
        self.resize(1200, 800)  # 设置更大的窗口尺寸
        self.center()  # 居中显示窗口

        # 加载css渲染效果
        try:
            # 尝试不同的路径
            style_files = [
                'UIProgram/style.css',
                'class_Detection_end5.1/class_Detection_end/UIProgram/style.css',
                'style.css'
            ]

            for style_file in style_files:
                try:
                    qssStyleSheet = QSSLoader.read_qss_file(style_file)
                    self.setStyleSheet(qssStyleSheet)
                    print(f"成功加载样式文件: {style_file}")
                    break
                except:
                    continue
        except Exception as e:
            print(f"加载样式文件失败: {e}")

        # 初始化
        self.initMain()
        self.signalconnect()

    def center(self):
        """将窗口居中显示"""
        qr = self.frameGeometry()
        cp = QtWidgets.QDesktopWidget().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def signalconnect(self):
        self.ui.PicBtn.clicked.connect(self.open_img)
        self.ui.comboBox.activated.connect(self.combox_change)
        self.ui.VideoBtn.clicked.connect(self.vedio_show)
        self.ui.CapBtn.clicked.connect(self.camera_show)
        self.ui.SaveBtn.clicked.connect(self.save_detect_video)
        self.ui.ExitBtn.clicked.connect(QCoreApplication.quit)
        self.ui.FilesBtn.clicked.connect(self.detact_batch_imgs)

    def initMain(self):
        self.show_width = 770
        self.show_height = 480

        self.org_path = None

        self.is_camera_open = False
        self.cap = None

        # 保存原始背景图片
        self.original_background = "1.png"

        # 设置背景图片
        self.set_background_image()

        # 创建动物介绍文本区域
        self.animal_info_text = QtWidgets.QTextEdit(self.ui.groupBox_2)
        self.animal_info_text.setGeometry(QtCore.QRect(10, 150, 410, 180))
        self.animal_info_text.setReadOnly(True)

        # 使用更精美的样式
        self.animal_info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f0f9f4;
                border: 2px solid #27ae60;
                border-radius: 10px;
                padding: 12px;
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                color: #2c3e50;
                line-height: 1.6;
            }
            QTextEdit:focus {
                border: 2px solid #16a085;
                background-color: #ffffff;
            }
        """)
        self.animal_info_text.setPlaceholderText("选择或检测到爬行动物后将在此显示详细介绍...")

        # 添加欢迎信息
        welcome_html = """
        <div style="text-align: center; margin: 5px; font-family: 'Microsoft YaHei', sans-serif;">
            <h2 style="color: #27ae60; margin-bottom: 12px; font-size: 20px;">欢迎使用爬行动物百科</h2>
            <p style="color: #7f8c8d; font-style: italic; margin-bottom: 10px;">请选择图片或视频进行爬行动物检测</p>

            <div style="background-color: #e8f8f5; border-radius: 8px; padding: 10px; margin: 10px 0; border-left: 4px solid #27ae60;">
                <p style="text-align: left; margin: 0 0 8px 0; font-weight: bold; color: #16a085;">操作指南：</p>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
                    <div style="text-align: left; width: 48%;">
                        <p style="margin: 5px 0;"><span style="color: #27ae60; font-weight: bold;">📷</span> 导入单张图片</p>
                        <p style="margin: 5px 0;"><span style="color: #27ae60; font-weight: bold;">🖼️</span> 导入多张图片</p>
                    </div>
                    <div style="text-align: left; width: 48%;">
                        <p style="margin: 5px 0;"><span style="color: #27ae60; font-weight: bold;">🎬</span> 导入视频文件</p>
                        <p style="margin: 5px 0;"><span style="color: #27ae60; font-weight: bold;">📹</span> 启动摄像头</p>
                    </div>
                </div>
            </div>

            <div style="margin: 10px 0;">
                <p style="text-align: left; margin: 5px 0; font-weight: bold; color: #16a085;">可识别的爬行动物：</p>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
                    <div style="text-align: left; width: 48%;">
                        <p style="margin: 5px 0;"><span style="font-weight: bold;">🐢</span> 乌龟 (Turtle)</p>
                        <p style="margin: 5px 0;"><span style="font-weight: bold;">🐍</span> 蛇 (Snake)</p>
                        <p style="margin: 5px 0;"><span style="font-weight: bold;">🦎</span> 蜥蜴 (Lizard)</p>
                    </div>
                    <div style="text-align: left; width: 48%;">
                        <p style="margin: 5px 0;"><span style="font-weight: bold;">🐊</span> 鳄鱼 (Crocodile)</p>
                        <p style="margin: 5px 0;"><span style="font-weight: bold;">🦎</span> 壁虎 (Gecko)</p>
                        <p style="margin: 5px 0;"><span style="font-weight: bold;">🦎</span> 鬣蜥 (Iguana)</p>
                    </div>
                </div>
            </div>
        </div>
        """
        self.animal_info_text.setHtml(welcome_html)
        self.animal_info_text.setVisible(True)

        # 隐藏原来的进度条区域
        self.ui.frame_7.setVisible(False)

        # 创建一个模拟模型类，用于在无法加载真实模型时提供基本功能
        class MockModel:
            def __init__(self):
                self.names = Config.names
                print("使用模拟模型")

            def __call__(self, img):
                # 返回一个模拟的结果对象
                class MockResults:
                    def __init__(self):
                        self.boxes = MockBoxes()

                    def plot(self):
                        # 如果输入是字符串（图片路径），则加载图片
                        if isinstance(img, str):
                            try:
                                import cv2
                                return cv2.imread(img)
                            except:
                                return np.zeros((480, 640, 3), dtype=np.uint8)
                        # 如果输入是numpy数组，则直接返回
                        return img

                class MockBoxes:
                    def __init__(self):
                        self.xyxy = []
                        self.cls = []
                        self.conf = []

                # 返回一个包含模拟结果的列表
                return [MockResults()]

        # 尝试加载真实模型
        try:
            print("尝试加载YOLOv8模型...")
            self.model = YOLO(Config.model_path, task='detect')
            # 预先加载推理模型
            self.model(np.zeros((48, 48, 3)))
            print("YOLOv8模型加载成功")
        except Exception as e:
            print(f"加载模型失败: {e}")
            # 使用模拟模型
            self.model = MockModel()

        # 使用系统默认字体
        try:
            self.fontC = ImageFont.truetype("simhei.ttf", 25, 0)  # 尝试使用黑体
        except:
            try:
                self.fontC = ImageFont.truetype("simsun.ttc", 25, 0)  # 尝试使用宋体
            except:
                # 如果都失败，使用PIL默认字体
                self.fontC = ImageFont.load_default()

        # 用于绘制不同颜色矩形框
        self.colors = tools.Colors()

        # 更新视频图像
        self.timer_camera = QTimer()

        # 保存视频
        self.timer_save_video = QTimer()

        # 表格设置
        self.ui.tableWidget.verticalHeader().setSectionResizeMode(QHeaderView.Fixed)
        self.ui.tableWidget.verticalHeader().setDefaultSectionSize(40)
        self.ui.tableWidget.setColumnWidth(0, 80)  # 设置列宽
        self.ui.tableWidget.setColumnWidth(1, 200)
        self.ui.tableWidget.setColumnWidth(2, 150)
        self.ui.tableWidget.setColumnWidth(3, 90)
        self.ui.tableWidget.setColumnWidth(4, 230)
        self.ui.tableWidget.setSelectionBehavior(QAbstractItemView.SelectRows)  # 设置表格整行选中
        self.ui.tableWidget.verticalHeader().setVisible(False)  # 隐藏列标题
        self.ui.tableWidget.setAlternatingRowColors(True)  # 表格背景交替

    def set_background_image(self):
        """设置背景图片"""
        try:
            # 检查图片是否存在
            if os.path.exists(self.original_background):
                # 设置背景图片
                self.ui.label_show.setStyleSheet(f"border-image: url({self.original_background});")
                print(f"成功设置背景图片: {self.original_background}")
            else:
                print(f"背景图片不存在: {self.original_background}")
        except Exception as e:
            print(f"设置背景图片失败: {e}")

    def open_img(self):
        if self.cap:
            # 打开图片前关闭摄像头
            self.video_stop()
            self.is_camera_open = False
            self.ui.CaplineEdit.setText('摄像头未开启')
            self.cap = None

        # 弹出的窗口名称：'打开图片'
        # 默认打开的目录：'./'
        file_path, _ = QFileDialog.getOpenFileName(None, '打开图片', './', "Image files (*.jpg *.jepg *.png)")
        if not file_path:
            # 如果用户取消选择，恢复背景图片
            self.set_background_image()
            return

        self.ui.comboBox.setDisabled(False)
        self.org_path = file_path
        self.org_img = tools.img_cvread(self.org_path)

        # 目标检测
        t1 = time.time()
        try:
            # 加载图片并进行检测
            self.results = self.model(self.org_path)[0]
            t2 = time.time()
            take_time_str = '{:.3f} s'.format(t2 - t1)
            self.ui.time_lb.setText(take_time_str)

            # 安全地获取检测结果
            try:
                location_list = self.results.boxes.xyxy.tolist() if hasattr(self.results.boxes, 'xyxy') and len(self.results.boxes.xyxy) > 0 else []
                self.location_list = [list(map(int, e)) for e in location_list] if location_list else []

                cls_list = self.results.boxes.cls.tolist() if hasattr(self.results.boxes, 'cls') and len(self.results.boxes.cls) > 0 else []
                self.cls_list = [int(i) for i in cls_list] if cls_list else []

                conf_list = self.results.boxes.conf.tolist() if hasattr(self.results.boxes, 'conf') and len(self.results.boxes.conf) > 0 else []
                self.conf_list = ['%.2f %%' % (each*100) for each in conf_list] if conf_list else []
            except Exception as e:
                print(f"处理检测结果时出错: {e}")
                # 如果出错，使用空列表
                self.location_list = []
                self.cls_list = []
                self.conf_list = []

            # 如果没有检测到任何目标，不添加模拟数据
            if not self.cls_list:
                print("未检测到任何爬行动物")

            total_nums = len(self.location_list)
            cls_percents = []
            for i in range(6):
                if total_nums > 0:
                    res = self.cls_list.count(i) / total_nums
                else:
                    res = 0
                cls_percents.append(res)
            self.set_percent(cls_percents)

            # 绘制检测结果
            now_img = self.results.plot()
            self.draw_img = now_img

            # 获取缩放后的图片尺寸
            self.img_width, self.img_height = self.get_resize_size(now_img)
            resize_cvimg = cv2.resize(now_img,(self.img_width, self.img_height))
            pix_img = tools.cvimg_to_qpiximg(resize_cvimg)
            self.ui.label_show.setPixmap(pix_img)
            self.ui.label_show.setAlignment(Qt.AlignCenter)

            # 设置路径显示
            self.ui.PiclineEdit.setText(self.org_path)

            # 目标数目
            target_nums = len(self.cls_list)
            self.ui.label_nums.setText(str(target_nums))

            # 设置目标选择下拉框
            choose_list = ['全部']
            target_names = [Config.names[id]+ '_'+ str(index) for index,id in enumerate(self.cls_list)]
            choose_list = choose_list + target_names

            self.ui.comboBox.clear()
            self.ui.comboBox.addItems(choose_list)

            if target_nums >= 1:
                self.ui.type_lb.setText(Config.CH_names[self.cls_list[0]])
                self.ui.label_conf.setText(str(self.conf_list[0]))
                # 默认显示第一个目标框坐标
                self.ui.label_xmin.setText(str(self.location_list[0][0]))
                self.ui.label_ymin.setText(str(self.location_list[0][1]))
                self.ui.label_xmax.setText(str(self.location_list[0][2]))
                self.ui.label_ymax.setText(str(self.location_list[0][3]))
                # 显示第一个检测到的动物的介绍
                self.show_animal_info(self.cls_list[0])
            else:
                self.ui.type_lb.setText('')
                self.ui.label_conf.setText('')
                self.ui.label_xmin.setText('')
                self.ui.label_ymin.setText('')
                self.ui.label_xmax.setText('')
                self.ui.label_ymax.setText('')

            # 删除表格所有行
            self.ui.tableWidget.setRowCount(0)
            self.ui.tableWidget.clearContents()
            self.tabel_info_show(self.location_list, self.cls_list, self.conf_list, path=self.org_path)
        except Exception as e:
            print(f"检测失败: {e}")
            # 如果检测失败，恢复背景图片
            self.set_background_image()


    def detact_batch_imgs(self):
        """处理多个图片文件"""
        if self.cap:
            # 打开图片前关闭摄像头
            self.video_stop()
            self.is_camera_open = False
            self.ui.CaplineEdit.setText('摄像头未开启')
            self.cap = None

        # 选择多个图片文件
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择多个图片文件",
            "./",
            "图片文件 (*.jpg *.jpeg *.png *.bmp)"
        )

        if not file_paths:
            return

        # 处理每个选中的图片
        for img_path in file_paths:
                # self.ui.comboBox.setDisabled(False)
                self.org_path = img_path  # 更新当前路径
                self.org_img = tools.img_cvread(img_path)
                # 目标检测
                t1 = time.time()
                self.results = self.model(img_path)[0]
                t2 = time.time()
                take_time_str = '{:.3f} s'.format(t2 - t1)
                self.ui.time_lb.setText(take_time_str)

                # 安全地获取检测结果
                try:
                    location_list = self.results.boxes.xyxy.tolist() if hasattr(self.results.boxes, 'xyxy') and len(self.results.boxes.xyxy) > 0 else []
                    self.location_list = [list(map(int, e)) for e in location_list] if location_list else []

                    cls_list = self.results.boxes.cls.tolist() if hasattr(self.results.boxes, 'cls') and len(self.results.boxes.cls) > 0 else []
                    self.cls_list = [int(i) for i in cls_list] if cls_list else []

                    conf_list = self.results.boxes.conf.tolist() if hasattr(self.results.boxes, 'conf') and len(self.results.boxes.conf) > 0 else []
                    self.conf_list = ['%.2f %%' % (each*100) for each in conf_list] if conf_list else []
                except Exception as e:
                    print(f"处理检测结果时出错: {e}")
                    # 如果出错，使用空列表
                    self.location_list = []
                    self.cls_list = []
                    self.conf_list = []

                # 如果没有检测到任何目标，不添加模拟数据
                if not self.cls_list:
                    print("未检测到任何爬行动物")

                total_nums = len(self.location_list)  # 使用self.location_list而不是location_list
                cls_percents = []
                for i in range(6):
                    if total_nums != 0:  # 防止除零错误
                        res = self.cls_list.count(i) / total_nums
                    else:
                        res = 0
                    cls_percents.append(res)
                self.set_percent(cls_percents)

                now_img = self.results.plot()

                self.draw_img = now_img
                # 获取缩放后的图片尺寸
                self.img_width, self.img_height = self.get_resize_size(now_img)
                resize_cvimg = cv2.resize(now_img, (self.img_width, self.img_height))
                pix_img = tools.cvimg_to_qpiximg(resize_cvimg)
                self.ui.label_show.setPixmap(pix_img)
                self.ui.label_show.setAlignment(Qt.AlignCenter)
                # 设置路径显示
                self.ui.PiclineEdit.setText(img_path)

                # 目标数目
                target_nums = len(self.cls_list)
                self.ui.label_nums.setText(str(target_nums))

                # 设置目标选择下拉框
                choose_list = ['全部']
                target_names = [Config.names[id] + '_' + str(index) for index, id in enumerate(self.cls_list)]
                choose_list = choose_list + target_names

                self.ui.comboBox.clear()
                self.ui.comboBox.addItems(choose_list)

                if target_nums >= 1:
                    self.ui.type_lb.setText(Config.CH_names[self.cls_list[0]])
                    self.ui.label_conf.setText(str(self.conf_list[0]))
                    #   默认显示第一个目标框坐标
                    #   设置坐标位置值
                    self.ui.label_xmin.setText(str(self.location_list[0][0]))
                    self.ui.label_ymin.setText(str(self.location_list[0][1]))
                    self.ui.label_xmax.setText(str(self.location_list[0][2]))
                    self.ui.label_ymax.setText(str(self.location_list[0][3]))
                else:
                    self.ui.type_lb.setText('')
                    self.ui.label_conf.setText('')
                    self.ui.label_xmin.setText('')
                    self.ui.label_ymin.setText('')
                    self.ui.label_xmax.setText('')
                    self.ui.label_ymax.setText('')

                # # 删除表格所有行
                # self.ui.tableWidget.setRowCount(0)
                # self.ui.tableWidget.clearContents()
                self.tabel_info_show(self.location_list, self.cls_list, self.conf_list, path=img_path)
                self.ui.tableWidget.scrollToBottom()
                QApplication.processEvents()  #刷新页面

    def draw_rect_and_tabel(self, results, img):
        """绘制检测框并更新界面信息"""
        # 复制原图，避免修改原始图像
        now_img = img.copy()

        # 获取检测结果
        location_list = results.boxes.xyxy.tolist()
        self.location_list = [list(map(int, e)) for e in location_list]
        cls_list = results.boxes.cls.tolist()
        self.cls_list = [int(i) for i in cls_list]
        self.conf_list = results.boxes.conf.tolist()
        self.conf_list = ['%.2f %%' % (each * 100) for each in self.conf_list]

        # 在图像上绘制检测框和标签
        for i in range(len(self.location_list)):
            loacation = self.location_list[i]
            type_id = int(self.cls_list[i])
            color = self.colors(type_id, True)
            now_img = tools.drawRectBox(now_img, loacation, Config.CH_names[type_id], self.fontC, color)

        # 调整图像大小并显示
        self.img_width, self.img_height = self.get_resize_size(now_img)
        resize_cvimg = cv2.resize(now_img, (self.img_width, self.img_height))
        pix_img = tools.cvimg_to_qpiximg(resize_cvimg)
        self.ui.label_show.setPixmap(pix_img)
        self.ui.label_show.setAlignment(Qt.AlignCenter)

        # 设置路径显示
        self.ui.PiclineEdit.setText(self.org_path)

        # 更新目标数目
        target_nums = len(self.cls_list)
        self.ui.label_nums.setText(str(target_nums))

        # 更新第一个目标的详细信息
        if target_nums >= 1:
            self.ui.type_lb.setText(Config.CH_names[self.cls_list[0]])
            self.ui.label_conf.setText(str(self.conf_list[0]))
            self.ui.label_xmin.setText(str(self.location_list[0][0]))
            self.ui.label_ymin.setText(str(self.location_list[0][1]))
            self.ui.label_xmax.setText(str(self.location_list[0][2]))
            self.ui.label_ymax.setText(str(self.location_list[0][3]))
        else:
            # 如果没有检测到目标，清空信息
            self.ui.type_lb.setText('')
            self.ui.label_conf.setText('')
            self.ui.label_xmin.setText('')
            self.ui.label_ymin.setText('')
            self.ui.label_xmax.setText('')
            self.ui.label_ymax.setText('')

        # 更新表格信息
        self.ui.tableWidget.setRowCount(0)
        self.ui.tableWidget.clearContents()
        self.tabel_info_show(self.location_list, self.cls_list, self.conf_list, path=self.org_path)

        return now_img

    def combox_change(self):
        """处理下拉框选择变化"""
        com_text = self.ui.comboBox.currentText()
        if com_text == '全部':
            cur_box = self.location_list
            cur_img = self.results.plot()
            if len(self.cls_list) > 0:
                self.ui.type_lb.setText(Config.CH_names[self.cls_list[0]])
                self.ui.label_conf.setText(str(self.conf_list[0]))
                # 显示第一个动物的介绍
                self.show_animal_info(self.cls_list[0])
        else:
            index = int(com_text.split('_')[-1])
            cur_box = [self.location_list[index]]
            cur_img = self.results[index].plot()
            self.ui.type_lb.setText(Config.CH_names[self.cls_list[index]])
            self.ui.label_conf.setText(str(self.conf_list[index]))
            # 显示选中动物的介绍
            self.show_animal_info(self.cls_list[index])

        # 设置坐标位置值
        if len(cur_box) > 0:
            self.ui.label_xmin.setText(str(cur_box[0][0]))
            self.ui.label_ymin.setText(str(cur_box[0][1]))
            self.ui.label_xmax.setText(str(cur_box[0][2]))
            self.ui.label_ymax.setText(str(cur_box[0][3]))

        resize_cvimg = cv2.resize(cur_img, (self.img_width, self.img_height))
        pix_img = tools.cvimg_to_qpiximg(resize_cvimg)
        self.ui.label_show.clear()
        self.ui.label_show.setPixmap(pix_img)
        self.ui.label_show.setAlignment(Qt.AlignCenter)

    def show_animal_info(self, animal_id):
        """显示动物介绍信息"""
        # 在文本区域显示动物介绍
        if animal_id in Config.animal_info:
            animal_name = Config.CH_names[animal_id]
            english_name = Config.names[animal_id]
            info = Config.animal_info[animal_id]

            # 设置HTML格式的文本，更加美观的百科风格
            html_content = f"""
            <div style="margin: 5px; font-family: 'Microsoft YaHei', sans-serif;">
                <div style="text-align: center; margin-bottom: 15px;">
                    <h2 style="color: #27ae60; margin: 0 0 5px 0; font-size: 20px;">{animal_name}</h2>
                    <p style="color: #7f8c8d; font-style: italic; margin: 0; font-size: 14px;">{english_name}</p>
                </div>

                <div style="background-color: #e8f8f5; border-radius: 8px; padding: 12px; margin: 10px 0; border-left: 4px solid #27ae60;">
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 10px;">
                        <tr>
                            <td style="width: 80px; padding: 5px; vertical-align: top;">
                                <span style="font-weight: bold; color: #16a085;">【分类】</span>
                            </td>
                            <td style="padding: 5px;">
                                爬行纲
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 5px; vertical-align: top;">
                                <span style="font-weight: bold; color: #16a085;">【特点】</span>
                            </td>
                            <td style="padding: 5px;">
                                冷血动物，体表覆盖鳞片或甲壳
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="background-color: #f0f9f4; border: 1px solid #27ae60; border-radius: 8px; padding: 12px; margin: 10px 0;">
                    <span style="font-weight: bold; color: #16a085; display: block; margin-bottom: 8px; font-size: 15px;">【详细介绍】</span>
                    <p style="text-indent: 2em; line-height: 1.6; margin: 0; text-align: justify;">{info}</p>
                </div>
            </div>
            """

            # 更新文本区域内容
            self.animal_info_text.setHtml(html_content)

            # 显示一个小的提示框，只在首次检测时显示
            if not hasattr(self, '_showed_info_tip'):
                # 创建自定义样式的消息框
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("检测成功")
                msg_box.setIcon(QMessageBox.Information)

                # 使用HTML格式设置更美观的文本
                msg_box.setText(f"""
                <div style="text-align: center;">
                    <h3 style="color: #27ae60; margin: 5px 0;">已检测到: {animal_name}</h3>
                    <img src="1.png" width="100" height="100" style="margin: 10px; border-radius: 5px;">
                </div>
                """)

                msg_box.setInformativeText("""
                <div style="margin: 10px 0;">
                    详细介绍已显示在<b style="color: #27ae60;">爬行动物百科</b>区域
                </div>
                """)

                # 添加不再显示的选项
                cb = QtWidgets.QCheckBox("不再显示此提示")
                cb.setStyleSheet("""
                    QCheckBox {
                        font-family: 'Microsoft YaHei';
                        font-size: 12px;
                        color: #34495e;
                    }
                    QCheckBox::indicator {
                        width: 15px;
                        height: 15px;
                    }
                    QCheckBox::indicator:unchecked {
                        border: 2px solid #bdc3c7;
                        background-color: white;
                        border-radius: 3px;
                    }
                    QCheckBox::indicator:checked {
                        border: 2px solid #27ae60;
                        background-color: #27ae60;
                        border-radius: 3px;
                    }
                """)
                msg_box.setCheckBox(cb)

                # 自定义按钮
                msg_box.setStandardButtons(QMessageBox.Ok)
                ok_button = msg_box.button(QMessageBox.Ok)
                ok_button.setText("了解")
                ok_button.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border-radius: 5px;
                        padding: 5px 15px;
                        font-weight: bold;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #2ecc71;
                    }
                    QPushButton:pressed {
                        background-color: #16a085;
                    }
                """)

                # 显示消息框
                msg_box.exec_()

                # 如果用户选择了不再显示，则记录此设置
                if cb.isChecked():
                    self._showed_info_tip = True


    def get_video_path(self):
        file_path, _ = QFileDialog.getOpenFileName(None, '打开视频', './', "Image files (*.avi *.mp4 *.jepg *.png)")
        if not file_path:
            return None
        self.org_path = file_path
        self.ui.VideolineEdit.setText(file_path)
        return file_path

    def video_start(self):
        # 删除表格所有行
        self.ui.tableWidget.setRowCount(0)
        self.ui.tableWidget.clearContents()

        # 清空下拉框
        self.ui.comboBox.clear()

        # 定时器开启，每隔一段时间，读取一帧
        self.timer_camera.start(1)
        self.timer_camera.timeout.connect(self.open_frame)

    def tabel_info_show(self, locations, clses, confs, path=None):
        path = path
        for location, cls, conf in zip(locations, clses, confs):
            row_count = self.ui.tableWidget.rowCount()  # 返回当前行数(尾部)
            self.ui.tableWidget.insertRow(row_count)  # 尾部插入一行
            item_id = QTableWidgetItem(str(row_count+1))  # 序号
            item_id.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)  # 设置文本居中
            item_path = QTableWidgetItem(str(path))  # 路径
            # item_path.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)

            item_cls = QTableWidgetItem(str(Config.CH_names[cls]))
            item_cls.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)  # 设置文本居中

            item_conf = QTableWidgetItem(str(conf))
            item_conf.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)  # 设置文本居中

            item_location = QTableWidgetItem(str(location)) # 目标框位置
            # item_location.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)  # 设置文本居中

            self.ui.tableWidget.setItem(row_count, 0, item_id)
            self.ui.tableWidget.setItem(row_count, 1, item_path)
            self.ui.tableWidget.setItem(row_count, 2, item_cls)
            self.ui.tableWidget.setItem(row_count, 3, item_conf)
            self.ui.tableWidget.setItem(row_count, 4, item_location)
        self.ui.tableWidget.scrollToBottom()

    def video_stop(self):
        self.cap.release()
        self.timer_camera.stop()
        # 恢复背景图片
        self.set_background_image()

    def open_frame(self):
        ret, now_img = self.cap.read()
        if ret:
            # 目标检测
            t1 = time.time()
            results = self.model(now_img)[0]
            t2 = time.time()
            take_time_str = '{:.3f} s'.format(t2 - t1)
            self.ui.time_lb.setText(take_time_str)

            location_list = results.boxes.xyxy.tolist()
            self.location_list = [list(map(int, e)) for e in location_list]
            cls_list = results.boxes.cls.tolist()
            self.cls_list = [int(i) for i in cls_list]
            self.conf_list = results.boxes.conf.tolist()
            self.conf_list = ['%.2f %%' % (each * 100) for each in self.conf_list]

            total_nums = len(location_list)
            cls_percents = []
            for i in range(6):
                if total_nums!= 0 :  #识别到的目标数量
                    res = self.cls_list.count(i) / total_nums
                else :
                    res=0
                #res = self.cls_list.count(i) / total_nums
                cls_percents.append(res)
            self.set_percent(cls_percents)

            now_img = results.plot()

            # 获取缩放后的图片尺寸
            self.img_width, self.img_height = self.get_resize_size(now_img)
            resize_cvimg = cv2.resize(now_img, (self.img_width, self.img_height))
            pix_img = tools.cvimg_to_qpiximg(resize_cvimg)
            self.ui.label_show.setPixmap(pix_img)
            self.ui.label_show.setAlignment(Qt.AlignCenter)

            # 目标数目
            target_nums = len(self.cls_list)
            self.ui.label_nums.setText(str(target_nums))

            # 设置目标选择下拉框
            choose_list = ['全部']
            target_names = [Config.names[id] + '_' + str(index) for index, id in enumerate(self.cls_list)]
            # object_list = sorted(set(self.cls_list))
            # for each in object_list:
            #     choose_list.append(Config.CH_names[each])
            choose_list = choose_list + target_names

            self.ui.comboBox.clear()
            self.ui.comboBox.addItems(choose_list)

            if target_nums >= 1:
                self.ui.type_lb.setText(Config.CH_names[self.cls_list[0]])
                self.ui.label_conf.setText(str(self.conf_list[0]))
                #   默认显示第一个目标框坐标
                #   设置坐标位置值
                self.ui.label_xmin.setText(str(self.location_list[0][0]))
                self.ui.label_ymin.setText(str(self.location_list[0][1]))
                self.ui.label_xmax.setText(str(self.location_list[0][2]))
                self.ui.label_ymax.setText(str(self.location_list[0][3]))
            else:
                self.ui.type_lb.setText('')
                self.ui.label_conf.setText('')
                self.ui.label_xmin.setText('')
                self.ui.label_ymin.setText('')
                self.ui.label_xmax.setText('')
                self.ui.label_ymax.setText('')


            # 删除表格所有行
            # self.ui.tableWidget.setRowCount(0)
            # self.ui.tableWidget.clearContents()
            self.tabel_info_show(self.location_list, self.cls_list, self.conf_list, path=self.org_path)

        else:
            self.cap.release()
            self.timer_camera.stop()

    def vedio_show(self):
        if self.is_camera_open:
            self.is_camera_open = False
            self.ui.CaplineEdit.setText('摄像头未开启')

        video_path = self.get_video_path()
        if not video_path:
            return None
        self.cap = cv2.VideoCapture(video_path)
        self.video_start()
        self.ui.comboBox.setDisabled(True)

    def camera_show(self):
        self.is_camera_open = not self.is_camera_open
        if self.is_camera_open:
            self.ui.CaplineEdit.setText('摄像头开启')
            self.cap = cv2.VideoCapture(0)
            self.video_start()
            self.ui.comboBox.setDisabled(True)
        else:
            self.ui.CaplineEdit.setText('摄像头未开启')
            self.ui.label_show.setText('')
            if self.cap:
                self.cap.release()
                cv2.destroyAllWindows()
            self.ui.label_show.clear()
            # 恢复背景图片
            self.set_background_image()

    def get_resize_size(self, img):
        """计算图像的缩放尺寸，保持原始比例"""
        _img = img.copy()
        # 获取图像尺寸
        if len(_img.shape) == 3:
            img_height, img_width, _ = _img.shape  # 彩色图像
        else:
            img_height, img_width = _img.shape  # 灰度图像

        # 计算缩放比例
        ratio = img_width / img_height

        # 根据比例调整尺寸
        if ratio >= self.show_width / self.show_height:
            self.img_width = self.show_width
            self.img_height = int(self.img_width / ratio)
        else:
            self.img_height = self.show_height
            self.img_width = int(self.img_height * ratio)

        return self.img_width, self.img_height

    def save_detect_video(self):
        if self.cap is None and not self.org_path:
            QMessageBox.about(self, '提示', '当前没有可保存信息，请先打开图片或视频！')
            return

        if self.is_camera_open:
            QMessageBox.about(self, '提示', '摄像头视频无法保存!')
            return

        if self.cap:
            res = QMessageBox.information(self, '提示', '保存视频检测结果可能需要较长时间，请确认是否继续保存？',QMessageBox.Yes | QMessageBox.No ,  QMessageBox.Yes)
            if res == QMessageBox.Yes:
                self.video_stop()
                com_text = self.ui.comboBox.currentText()
                self.btn2Thread_object = btn2Thread(self.org_path, self.model, com_text)
                self.btn2Thread_object.start()
                self.btn2Thread_object.update_ui_signal.connect(self.update_process_bar)
            else:
                return
        else:
            if os.path.isfile(self.org_path):
                fileName = os.path.basename(self.org_path)
                name , end_name= fileName.rsplit(".",1)
                save_name = name + '_detect_result.' + end_name
                save_img_path = os.path.join(Config.save_path, save_name)
                # 保存图片
                cv2.imwrite(save_img_path, self.draw_img)
                QMessageBox.about(self, '提示', '图片保存成功!\n文件路径:{}'.format(save_img_path))
            else:
                img_suffix = ['jpg', 'png', 'jpeg', 'bmp']
                for file_name in os.listdir(self.org_path):
                    full_path = os.path.join(self.org_path, file_name)
                    if os.path.isfile(full_path) and file_name.split('.')[-1].lower() in img_suffix:
                        name, end_name = file_name.rsplit(".",1)
                        save_name = name + '_detect_result.' + end_name
                        save_img_path = os.path.join(Config.save_path, save_name)
                        results = self.model(full_path)[0]
                        now_img = results.plot()
                        # 保存图片
                        cv2.imwrite(save_img_path, now_img)

                QMessageBox.about(self, '提示', '图片保存成功!\n文件路径:{}'.format(Config.save_path))


    def update_process_bar(self,cur_num, total):
        if cur_num == 1:
            self.progress_bar = ProgressBar(self)
            self.progress_bar.show()
        if cur_num >= total:
            self.progress_bar.close()
            QMessageBox.about(self, '提示', '视频保存成功!\n文件在{}目录下'.format(Config.save_path))
            return
        if self.progress_bar.isVisible() is False:
            # 点击取消保存时，终止进程
            self.btn2Thread_object.stop()
            return
        value = int(cur_num / total *100)
        self.progress_bar.setValue(cur_num, total, value)
        QApplication.processEvents()

    def set_percent(self, probs):
        # 显示各表情概率值
        items = [self.ui.progressBar, self.ui.progressBar_2, self.ui.progressBar_3, self.ui.progressBar_4,
                 self.ui.progressBar_5, self.ui.progressBar_6]
        labels = [self.ui.label_20, self.ui.label_21, self.ui.label_22, self.ui.label_23,
                  self.ui.label_24, self.ui.label_25]
        prob_values = [round(each * 100) for each in probs]
        label_values = ['{:.1f}%'.format(each * 100) for each in probs]
        for i in range(len(probs)):
            items[i].setValue(prob_values[i])
            labels[i].setText(label_values[i])


class btn2Thread(QThread):
    """
    进行检测后的视频保存
    """
    # 声明一个信号
    update_ui_signal = pyqtSignal(int,int)

    def __init__(self, path, model, com_text):
        super(btn2Thread, self).__init__()
        self.org_path = path
        self.model = model
        self.com_text = com_text
        # 用于绘制不同颜色矩形框
        self.colors = tools.Colors()
        self.is_running = True  # 标志位，表示线程是否正在运行

    def run(self):
        # VideoCapture方法是cv2库提供的读取视频方法
        cap = cv2.VideoCapture(self.org_path)
        # 设置需要保存视频的格式“xvid”
        # 该参数是MPEG-4编码类型，文件名后缀为.avi
        fourcc = cv2.VideoWriter_fourcc(*'XVID')
        # 设置视频帧频
        fps = cap.get(cv2.CAP_PROP_FPS)
        # 设置视频大小
        size = (int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)))
        # VideoWriter方法是cv2库提供的保存视频方法
        # 按照设置的格式来out输出
        fileName = os.path.basename(self.org_path)
        # 安全地分割文件名和扩展名
        if '.' in fileName:
            name = fileName.split('.')[0]
        else:
            name = fileName

        save_name = name + '_detect_result.avi'
        save_video_path = os.path.join(Config.save_path, save_name)
        out = cv2.VideoWriter(save_video_path, fourcc, fps, size)

        prop = cv2.CAP_PROP_FRAME_COUNT
        total = int(cap.get(prop))
        print("[INFO] 视频总帧数：{}".format(total))
        cur_num = 0

        # 确定视频打开并循环读取
        while (cap.isOpened() and self.is_running):
            cur_num += 1
            print('当前第{}帧，总帧数{}'.format(cur_num, total))
            # 逐帧读取，ret返回布尔值
            # 参数ret为True 或者False,代表有没有读取到图片
            # frame表示截取到一帧的图片
            ret, frame = cap.read()
            if ret == True:
                # 检测
                results = self.model(frame)[0]
                frame = results.plot()
                out.write(frame)
                self.update_ui_signal.emit(cur_num, total)
            else:
                break
        # 释放资源
        cap.release()
        out.release()

    def stop(self):
        self.is_running = False




if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
